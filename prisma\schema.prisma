
generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-1.1.x"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

enum Role {
  user
  admin
}

model User {
  id            String    @id
  name          String    @db.Text
  email         String
  emailVerified Boolean
  image         String?   @db.Text
  createdAt     DateTime
  updatedAt     DateTime
  role          Role      @default(user)
  banned        Boolean?
  banReason     String?   @db.Text
  banExpires    DateTime?
  sessions      Session[]
  accounts      Account[]

  @@unique([email])
  @@map("user")
}

model Session {
  id             String   @id
  expiresAt      DateTime
  token          String
  createdAt      DateTime
  updatedAt      DateTime
  ipAddress      String?  @db.Text
  userAgent      String?  @db.Text
  userId         String
  user           User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  impersonatedBy String?  @db.Text

  @@unique([token])
  @@map("session")
}

model Account {
  id                    String    @id
  accountId             String    @db.Text
  providerId            String    @db.Text
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?   @db.Text
  refreshToken          String?   @db.Text
  idToken               String?   @db.Text
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?   @db.Text
  password              String?   @db.Text
  createdAt             DateTime
  updatedAt             DateTime

  @@map("account")
}

model Verification {
  id         String    @id
  identifier String    @db.Text
  value      String    @db.Text
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verification")
}
