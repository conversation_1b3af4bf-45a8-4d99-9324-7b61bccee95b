import { CSSProperties } from "react";

import { RiSideBarFill } from "@remixicon/react";
import { cookies } from "next/headers";

import GetAuthSession from "@/action/auth/get-auth-session";
import AppSidebar from "@/components/sidebar/app-sidebar";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import UserAvatar from "@/components/user-avatar";

const SIDEBAR_COOKIE_NAME = "sidebar_state";

export default async function SidebarLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const cookieStore = await cookies();
  const defaultOpen = cookieStore.get(SIDEBAR_COOKIE_NAME)?.value === "true";

  const session = await GetAuthSession();

  return (
    <SidebarProvider
      defaultOpen={defaultOpen}
      style={
        {
          "--sidebar-width": "20rem",
          "--sidebar-width-mobile": "20rem",
        } as CSSProperties
      }
    >
      <AppSidebar collapsible="offcanvas" />
      <SidebarInset className="flex h-screen flex-col overflow-hidden">
        <header className="bg-background sticky top-0 z-50 flex shrink-0 items-center gap-2 border-b p-4 transition-[margin-right] duration-200 ease-in-out">
          <SidebarTrigger className="-ml-1">
            <RiSideBarFill className="size-4" />
          </SidebarTrigger>

          <Separator
            orientation="vertical"
            className="mr-2 data-[orientation=vertical]:h-4"
          />

          <div className="ml-auto flex flex-row items-center gap-3">
            <UserAvatar session={session} />
          </div>
        </header>
        <main className="relative flex flex-1 flex-col gap-4 overflow-auto p-2 transition-[margin-right] duration-200 ease-in-out md:p-4">
          {children}
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}
