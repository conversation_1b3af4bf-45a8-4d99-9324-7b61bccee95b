import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ill,
  R<PERSON><PERSON>ailFill,
  RiTwitterXFill,
} from "@remixicon/react";
import { Metadata } from "next";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export const metadata: Metadata = {
  title: "Contact - Next Core",
  description: "Get in touch with us through various channels",
};

export default function ContactPage() {
  return (
    <div className="container mx-auto max-w-3xl py-12">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-semibold">Get in Touch</CardTitle>
        </CardHeader>
        <CardContent className="space-y-8">
          <p className="text-muted-foreground">
            Have questions or want to learn more? We&apos;d love to hear from
            you. Choose your preferred way to connect with us.
          </p>

          <div className="grid gap-6 sm:grid-cols-2">
            <a
              href="mailto:<EMAIL>"
              className="hover:bg-muted flex items-center gap-3 rounded-lg border p-4 transition-colors"
            >
              <RiMailFill className="h-5 w-5" />
              <div>
                <div className="font-medium">Email</div>
                <div className="text-muted-foreground text-sm">
                  <EMAIL>
                </div>
              </div>
            </a>

            <a
              href="https://twitter.com/nextcore"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:bg-muted flex items-center gap-3 rounded-lg border p-4 transition-colors"
            >
              <RiTwitterXFill className="h-5 w-5" />
              <div>
                <div className="font-medium">Twitter</div>
                <div className="text-muted-foreground text-sm">@nextcore</div>
              </div>
            </a>

            <a
              href="https://github.com/nextcore"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:bg-muted flex items-center gap-3 rounded-lg border p-4 transition-colors"
            >
              <RiGithubFill className="h-5 w-5" />
              <div>
                <div className="font-medium">GitHub</div>
                <div className="text-muted-foreground text-sm">
                  github.com/nextcore
                </div>
              </div>
            </a>

            <a
              href="https://linkedin.com/company/nextcore"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:bg-muted flex items-center gap-3 rounded-lg border p-4 transition-colors"
            >
              <RiLinkedinBoxFill className="h-5 w-5" />
              <div>
                <div className="font-medium">LinkedIn</div>
                <div className="text-muted-foreground text-sm">Next Core</div>
              </div>
            </a>
          </div>

          <div className="bg-muted rounded-lg p-4">
            <p className="text-muted-foreground text-sm">
              For business inquiries or partnership opportunities, please email
              us at{" "}
              <a
                href="mailto:<EMAIL>"
                className="text-primary font-medium hover:underline"
              >
                <EMAIL>
              </a>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
