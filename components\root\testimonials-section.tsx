"use client";

import Autoplay from "embla-carousel-autoplay";
import { ChevronRightIcon } from "lucide-react";
import { motion } from "motion/react";

import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";

const testimonials = [
  {
    name: "<PERSON>",
    role: "Product Manager",
    company: "TechCorp",
    content:
      "Next Core has transformed how we handle our development workflow. The attention to detail and modern features are exactly what we needed.",
    avatar: "/testimonials/avatar1.jpg",
  },
  {
    name: "<PERSON>",
    role: "Senior Developer",
    company: "InnovateLabs",
    content:
      "The developer experience is outstanding. The documentation is clear, and the features are well thought out. It's become an essential part of our tech stack.",
    avatar: "/testimonials/avatar2.jpg",
  },
  {
    name: "<PERSON>",
    role: "CT<PERSON>",
    company: "StartupX",
    content:
      "We've seen a significant boost in our team's productivity since implementing Next Core. The modern architecture and built-in features are game-changing.",
    avatar: "/testimonials/avatar3.jpg",
  },
  {
    name: "<PERSON>",
    role: "Software Engineer",
    company: "Acme Inc",
    content:
      "Next Core has been a game-changer for our team. The modern features and ease of use have significantly improved our development process.",
    avatar: "/testimonials/avatar4.jpg",
  },
  {
    name: "Olivia Martinez",
    role: "DevOps Engineer",
    company: "GlobalSoft",
    content:
      "Next Core has simplified our deployment and management processes. The built-in monitoring and alerting features have saved us a lot of time and effort.",
    avatar: "/testimonials/avatar5.jpg",
  },
];

export default function TestimonialsSection() {
  return (
    <section className="relative px-4 py-24 sm:px-6 lg:px-8">
      <div className="mx-auto max-w-7xl">
        {/* Section Header - Linear Style */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="mb-16"
        >
          {/* Category Label with Green Dot */}
          <div className="mb-8 flex items-center gap-2">
            <div className="h-2.5 w-4 animate-pulse rounded-full bg-green-500/80" />
            <span className="text-muted-foreground text-sm font-medium">
              Testimonials
            </span>
            <ChevronRightIcon className="text-muted-foreground h-4 w-4" />
          </div>

          <h2 className="from-primary via-primary/80 to-primary/70 mb-8 bg-gradient-to-r bg-clip-text text-5xl leading-tight font-medium text-transparent lg:text-6xl">
            What our users say
          </h2>

          <div className="max-w-2xl">
            <p className="text-muted-foreground text-lg leading-relaxed">
              Don&apos;t just take our word for it. See what our users have to
              say about Next Core.
            </p>
          </div>
        </motion.div>

        {/* Testimonials Carousel */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
          className="relative"
        >
          <div className="from-background pointer-events-none absolute top-0 left-0 z-10 h-full w-32 bg-gradient-to-r to-transparent" />
          <div className="from-background pointer-events-none absolute top-0 right-0 z-10 h-full w-32 bg-gradient-to-l to-transparent" />
          <Carousel
            opts={{
              align: "start",
              loop: true,
            }}
            plugins={[
              Autoplay({
                delay: 3000,
              }),
            ]}
            className="mx-auto w-full"
          >
            <CarouselContent className="ml-2">
              {testimonials.map((testimonial, index) => (
                <CarouselItem
                  key={index}
                  className="cursor-pointer pl-2 select-none md:basis-1/2 lg:basis-1/3"
                >
                  <div className="bg-card/50 rounded-none border p-4">
                    <p className="text-muted-foreground mb-6 flex flex-col gap-2 rounded-none border border-dashed border-neutral-400/20 bg-neutral-100 p-4 font-mono leading-relaxed tracking-tighter shadow-2xs dark:bg-neutral-800">
                      &quot;{testimonial.content}&quot;
                    </p>
                    <div className="flex items-center gap-4">
                      <div className="h-10 w-10 rounded-full bg-neutral-200" />
                      <div>
                        <p className="text-foreground text-lg font-medium">
                          {testimonial.name}
                        </p>
                        <p className="text-muted-foreground text-sm">
                          {testimonial.role} at {testimonial.company}
                        </p>
                      </div>
                    </div>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>
        </motion.div>
      </div>
    </section>
  );
}
