import { ImageResponse } from "next/og";

// Image metadata
export const size = {
  width: 36,
  height: 36,
};

export const contentType = "image/svg+xml";

// Image generation
export default function Icon() {
  return new ImageResponse(
    (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        width="36"
        height="36"
        viewBox="0 0 100 100"
        style={{
          transform: "rotate(45deg)",
          transformOrigin: "center",
          background: "white",
          borderRadius: "100%",
        }}
      >
        <g fill="black">
          {/* Top circle */}
          <circle
            cx="50"
            cy="25"
            r="15"
            opacity="0.9"
          />
          {/* Right circle */}
          <circle
            cx="75"
            cy="50"
            r="15"
            opacity="0.9"
          />
          {/* Bottom circle */}
          <circle
            cx="50"
            cy="75"
            r="15"
            opacity="0.9"
          />
          {/* Left circle */}
          <circle
            cx="25"
            cy="50"
            r="15"
            opacity="0.9"
          />

          {/* Center overlapping area - creates the interlocking effect */}
          <circle
            cx="50"
            cy="50"
            r="8"
            fill="black"
          />

          {/* Cutout circles to create the interlocking pattern */}
          <circle
            cx="50"
            cy="35"
            r="6"
            fill="white"
          />
          <circle
            cx="65"
            cy="50"
            r="6"
            fill="white"
          />
          <circle
            cx="50"
            cy="65"
            r="6"
            fill="white"
          />
          <circle
            cx="35"
            cy="50"
            r="6"
            fill="white"
          />
        </g>
      </svg>
    ),
    // ImageResponse options
    {
      // For convenience, we can re-use the exported icons size metadata
      // config to also set the ImageResponse's width and height.
      ...size,
    },
  );
}
