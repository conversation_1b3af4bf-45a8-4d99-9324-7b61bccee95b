"use client";

import { ArrowRightIcon } from "lucide-react";
import { motion } from "motion/react";
import Image from "next/image";

import { MainMenusGradientCard } from "@/components/root/gradient-card";

export default function FeaturesSection() {
  return (
    <section className="bg-input/30 relative px-4 py-24 sm:px-6 lg:px-8">
      <div className="mx-auto max-w-7xl">
        {/* Section Header - Linear Style */}
        <div className="mb-16 grid grid-cols-1 items-center gap-12 lg:grid-cols-2">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            <h2 className="from-primary via-primary/80 to-primary/70 bg-gradient-to-r bg-clip-text text-5xl leading-tight font-medium text-transparent">
              Made for modern product teams
            </h2>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.1 }}
            className="space-y-4"
          >
            <p className="text-muted-foreground text-lg leading-relaxed font-normal">
              Next Core is shaped by the practices and principles that
              distinguish world-class product teams from the rest: relentless
              focus, fast execution, and a commitment to the quality of craft.
            </p>
            <div className="flex items-center">
              <span className="text-primary flex cursor-pointer items-center gap-2 text-sm font-medium hover:underline">
                Make the switch <ArrowRightIcon className="h-3 w-3" />
              </span>
            </div>
          </motion.div>
        </div>

        {/* Feature Cards Grid - Linear.app Style */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
          className="grid grid-cols-1 gap-2 md:grid-cols-2 lg:grid-cols-3"
        >
          {/* Card 1 - Purpose-built for product development */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.3 }}
          >
            <MainMenusGradientCard
              withArrow={true}
              className="group bg-card dark:bg-card/50 relative flex h-72 flex-col overflow-hidden border p-6"
              description="This is the best library for creating dynamic cards"
              title="Purpose-built "
            >
              <Image
                src="https://pbs.twimg.com/media/GgMiuRpa4AAoW2y?format=jpg&name=medium"
                alt=""
                fill
                priority
                sizes="100%"
                loading="eager"
                draggable={false}
                className="truncate object-cover"
              />
            </MainMenusGradientCard>
          </motion.div>

          {/* Card 2 - Designed to move fast */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.4 }}
          >
            <MainMenusGradientCard
              withArrow={true}
              className="group bg-card dark:bg-card/50 relative flex h-72 flex-col overflow-hidden border p-6"
              description="This is the best library for creating dynamic cards"
              title="Designed to move fast"
            >
              <Image
                src="https://pbs.twimg.com/media/GgHZJN0aoAA__92?format=jpg&name=medium"
                alt=""
                fill
                priority
                sizes="100%"
                loading="eager"
                draggable={false}
                className="truncate object-cover"
              />
            </MainMenusGradientCard>
          </motion.div>

          {/* Card 3 - Crafted to perfection */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.5 }}
          >
            <MainMenusGradientCard
              withArrow={true}
              className="group bg-card dark:bg-card/50 relative flex h-72 flex-col overflow-hidden border p-6"
              description="This is the best library for creating dynamic cards"
              title="Crafted to perfection"
            >
              <Image
                src="https://pbs.twimg.com/media/GgCPjsQacAAWvm3?format=jpg&name=medium"
                alt=""
                fill
                priority
                sizes="100%"
                loading="eager"
                draggable={false}
                className="truncate object-cover"
              />
            </MainMenusGradientCard>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
