import GetAuthSession from "@/action/auth/get-auth-session";
import { polarClient } from "@/lib/polar";

export default async function BillingPage() {
  const session = await GetAuthSession();

  if (!session) {
    return;
  }

  const customer = await polarClient.customers.getStateExternal({
    externalId: session.user.id,
  });

  const subscriptions = customer.activeSubscriptions[0];

  if (!subscriptions) {
    return;
  }

  const product = await polarClient.products.get({
    id: subscriptions.productId,
  });

  return (
    <div className="mx-auto max-w-4xl p-6">
      <h1 className="mb-6 text-2xl font-semibold">Subscription Details</h1>
      <div className="bg-card rounded-lg border p-6">
        <div className="grid gap-4">
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Plan</span>
            <span className="font-medium">
              {product?.id || "No active plan"}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Status</span>
            <span className="font-medium">
              {subscriptions?.status || "Inactive"}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Next billing date</span>
            <span className="font-medium">
              {subscriptions?.currentPeriodEnd
                ? new Date(subscriptions.currentPeriodEnd).toLocaleDateString()
                : "N/A"}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
