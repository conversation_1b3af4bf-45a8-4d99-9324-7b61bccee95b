import Link from "next/link";
import { redirect } from "next/navigation";

import { But<PERSON> } from "@/components/ui/button";

export default async function SuccessPage({
  searchParams,
}: {
  searchParams: Promise<{ checkout_id?: string }>;
}) {
  const { checkout_id: checkoutId } = await searchParams;

  if (!checkoutId) {
    redirect("/");
  }

  return (
    <main className="min-h-screen space-y-12">
      <div className="flex flex-col items-center justify-center gap-8 px-4 py-24">
        <div className="flex flex-col items-center gap-4 text-center">
          <h1 className="from-primary via-primary/80 to-primary/70 mb-6 bg-gradient-to-r bg-clip-text text-3xl leading-tight font-medium tracking-tight text-transparent sm:text-4xl lg:text-6xl">
            Thank you for your purchase!
          </h1>
        </div>
        <div className="flex flex-col items-center gap-4">
          <Button asChild>
            <Link
              href="/dashboard"
              className="bg-primary text-primary-foreground hover:bg-primary/90 inline-flex items-center justify-center rounded-md px-6 py-2.5 text-sm font-medium transition-colors"
            >
              Go to Dashboard
            </Link>
          </Button>
        </div>
      </div>
    </main>
  );
}
