export const plans = [
  {
    name: "basic",
    limits: {
      tokens: 100,
    },
    price: "$0",
    description: "Free for everyone",
    features: [
      "All Free features +",
      "5 teams",
      "Unlimited issues",
      "Unlimited file uploads",
      "Admin roles",
    ],
    buttonText: "Get started",
    popular: false,
  },
  {
    name: "pro",
    productId: "4fc06ed0-c47c-4df2-9618-2b77a5040242",
    limits: {
      tokens: 500,
    },
    price: "$30",
    period: "per user/month",
    billing: "Billed yearly",
    description: "For growing teams",
    features: [
      "All Free features +",
      "5 teams",
      "Unlimited issues",
      "Unlimited file uploads",
      "Admin roles",
    ],
    buttonText: "Get started",
    popular: false,
  },
  {
    name: "business",
    productId: "f08366f6-e7b6-475d-9ac3-f282e93214fb",
    limits: {
      tokens: 1000,
    },
    price: "$100",
    period: "per user/month",
    billing: "Billed yearly",
    description: "For established teams",
    features: [
      "All Basic features +",
      "Next Core Asks",
      "Unlimited teams",
      "Private teams and guests",
      "Next Core Insights",
      "Triage responsibility",
    ],
    buttonText: "Get started",
    popular: true,
  },
  {
    name: "enterprise",
    limits: {
      tokens: 1000,
    },
    price: "Contact us",
    billing: "Annual billing only",
    description: "For large organizations",
    features: [
      "All Business features +",
      "Advanced Next Core Asks",
      "Issue SLAs",
      "SAML and SCIM",
      "Advanced security",
      "Migration and onboarding support",
    ],
    buttonText: "Request trial",
    popular: false,
  },
];

export type Plan = (typeof plans)[number];
