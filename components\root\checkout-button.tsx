"use client";

import { useTransition } from "react";

import { useRouter } from "@bprogress/next";
import { toast } from "sonner";

import { Session } from "@/auth";
import { authClient } from "@/auth-client";
import { ShinyRotatingBorderButton } from "@/components/root/shiny-rotating-border-button";
import { SIGN_IN_REDIRECT } from "@/routes";

interface CheckoutButtonProps {
  session: Session;
  buttonText: string;
  productId: string | undefined;
  slug: string;
}

export default function CheckoutButton({
  session,
  buttonText,
  productId,
  slug,
}: CheckoutButtonProps) {
  const router = useRouter();

  const [isPending, startTransition] = useTransition();

  function handleCheckout() {
    startTransition(async () => {
      if (slug === "enterprise") {
        router.push("/contact");
        return;
      }

      if (!session) {
        toast("Please sign in to continue", {
          description: "You need to be logged in to checkout",
          action: {
            label: "Log In",
            onClick: () => router.push(SIGN_IN_REDIRECT),
          },
        });
        return;
      }

      if (!productId) {
        router.push(SIGN_IN_REDIRECT);
        return;
      }

      try {
        await authClient.checkout({
          products: [productId],
          slug,
        });

        await new Promise((resolve) => setTimeout(resolve, 2000));
      } catch (error) {
        console.error("Checkout error:", error);
      }
    });
  }

  return (
    <ShinyRotatingBorderButton
      onClick={handleCheckout}
      disabled={isPending}
      className="!font-medium"
    >
      {buttonText}
    </ShinyRotatingBorderButton>
  );
}
