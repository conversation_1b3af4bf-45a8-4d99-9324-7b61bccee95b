"use client";

import React, { useEffect, useState, useTransition } from "react";

import { useRouter } from "@bprogress/next";
import { MenuIcon, XIcon } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";

import SignOut from "@/action/auth/sign-out";
import { Session } from "@/auth";
import ConfirmationDialog from "@/components/confirmation-dialog";
import Logo from "@/components/logo";
import ThemeSwitcher from "@/components/theme-switcher";
import { Button } from "@/components/ui/button";
import UserAvatar from "@/components/user-avatar";
import useDialog from "@/hooks/use-dialog";
import { cn } from "@/lib/utils";

const navigationLinks: { href: string; label: string }[] = [];

// Mobile Sign Out Button Component
function MobileSignOutButton() {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const { open, onOpenChange, openDialog, closeDialog } = useDialog();

  function handleSignOut() {
    startTransition(async () => {
      try {
        const result = await SignOut();
        if (result.success) {
          router.refresh();
        }
      } catch (error) {
        console.error("Sign out error:", error);
      }
    });
  }

  return (
    <>
      <Button
        variant="destructive"
        className="w-full text-sm"
        onClick={openDialog}
        disabled={isPending}
      >
        {isPending ? "Signing out..." : "Sign out"}
      </Button>

      <ConfirmationDialog
        open={open}
        onOpenChange={onOpenChange}
        closeDialog={closeDialog}
        onClick={handleSignOut}
        isPending={isPending}
        title="Are you sure you want to sign out?"
        description="This will log you out of your account"
        confirmText="Confirm"
        confirmVariant="destructive"
      />
    </>
  );
}

export default function Header({ session }: { session: Session }) {
  const pathname = usePathname();

  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [isScrolled, setIsScrolled] = useState<boolean>(false);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Close mobile menu when route changes
  useEffect(() => {
    setIsOpen(false);
  }, [pathname]);

  return (
    <>
      <nav
        className={cn(
          "fixed top-0 right-0 left-0 z-50 transition-all duration-300",
          isScrolled
            ? "bg-background/95 border-border border-b shadow-sm backdrop-blur-xl"
            : "bg-transparent",
        )}
      >
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="relative flex h-16 items-center">
            {/* Logo - Fixed Left */}
            <Logo withText={true} />

            {/* Desktop Navigation Links - Absolutely Centered */}
            <div className="absolute top-1/2 left-1/2 hidden -translate-x-1/2 -translate-y-1/2 md:block">
              <div className="flex items-center space-x-8">
                {navigationLinks.map((link, index) => (
                  <Link
                    key={link.href ?? index}
                    href={link.href ?? "#"}
                    className={cn(
                      "hover:text-primary relative px-3 py-2 text-sm font-medium transition-all duration-200",
                      pathname === link.href
                        ? "text-primary"
                        : "text-muted-foreground hover:text-foreground",
                    )}
                  >
                    {link.label}
                    {pathname === link.href && (
                      <div className="bg-primary absolute -bottom-1 left-1/2 h-0.5 w-6 -translate-x-1/2 rounded-full" />
                    )}
                  </Link>
                ))}
              </div>
            </div>

            {/* Desktop Auth Buttons / User Menu - Fixed Right */}
            <div className="ml-auto hidden items-center space-x-3 md:flex">
              {session?.user ? (
                <UserAvatar session={session} />
              ) : (
                <>
                  <ThemeSwitcher triggerClassName="size-8" />

                  <Button
                    size="sm"
                    className="text-sm shadow-sm"
                    asChild
                  >
                    <Link href="/auth/sign-in">Get Started</Link>
                  </Button>
                </>
              )}
            </div>

            {/* Mobile menu button - Fixed Right */}
            <div className="ml-auto md:hidden">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsOpen(!isOpen)}
                className="p-2"
                aria-label="Toggle menu"
              >
                {isOpen ? (
                  <XIcon className="h-6 w-6" />
                ) : (
                  <MenuIcon className="h-6 w-6" />
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        <div
          className={cn(
            "border-border bg-background/95 fixed inset-x-0 top-16 border-b backdrop-blur-xl transition-all duration-300 ease-in-out md:hidden",
            isOpen
              ? "visible translate-y-0 opacity-100"
              : "invisible -translate-y-4 opacity-0",
          )}
        >
          <div className="space-y-6 px-4 py-6">
            {/* Mobile Navigation Links */}
            <div className="space-y-1">
              {navigationLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className={cn(
                    "hover:text-primary block rounded-lg px-4 py-3 text-base font-medium transition-all duration-200",
                    pathname === link.href
                      ? "bg-primary/10 text-primary"
                      : "text-muted-foreground hover:bg-muted hover:text-foreground",
                  )}
                >
                  {link.label}
                </Link>
              ))}
            </div>

            {/* Mobile Auth Buttons / User Menu */}
            <div className="space-y-3 border-t pt-6">
              {session?.user ? (
                <>
                  {/* User Info */}
                  <div className="flex items-center space-x-3 px-4 py-3">
                    {session.user.image ? (
                      <Image
                        src={session.user.image}
                        alt={session.user.name}
                        width={40}
                        height={40}
                        priority
                        className="rounded-full"
                        draggable={false}
                      />
                    ) : (
                      <div className="bg-primary text-primary-foreground flex h-10 w-10 items-center justify-center rounded-full text-sm font-medium">
                        {session.user.name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")
                          .toUpperCase()
                          .slice(0, 2)}
                      </div>
                    )}
                    <div className="flex flex-col">
                      <p className="text-sm font-medium">{session.user.name}</p>
                      <p className="text-muted-foreground text-xs">
                        {session.user.email}
                      </p>
                    </div>
                  </div>

                  <Button
                    variant="outline"
                    className="w-full text-sm"
                    asChild
                  >
                    <Link href="/dashboard">Dashboard</Link>
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full text-sm shadow-sm"
                    asChild
                  >
                    <Link href="/settings"> Settings</Link>
                  </Button>

                  <MobileSignOutButton />
                </>
              ) : (
                <Button
                  className="w-full text-sm shadow-sm"
                  asChild
                >
                  <Link href="/auth/sign-in">Get Started</Link>
                </Button>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile menu overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/20 backdrop-blur-sm md:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}
    </>
  );
}
