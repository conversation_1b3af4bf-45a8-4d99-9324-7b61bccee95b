import BProgressProvider from "@/components/bprogress-provider";
import ThemeProvider from "@/components/theme-provider";
import { Toaster } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";

export default function Providers({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange={true}
    >
      <BProgressProvider
        color="var(--primary)"
        options={{
          showSpinner: false,
          easing: "ease-out",
          speed: 350,
          trickle: true,
          trickleSpeed: 200,
          minimum: 0.08,
        }}
        height="4px"
        shallowRouting={true}
      >
        <TooltipProvider>
          <>{children}</>
        </TooltipProvider>
        <Toaster
          position="bottom-right"
          richColors={true}
        />
      </BProgressProvider>
    </ThemeProvider>
  );
}
