"use client";

import { type ReactNode, useId } from "react";

import { motion } from "motion/react";

import { cn } from "@/lib/utils";

export const ShinyRotatingBorderButton = ({
  children,
  className,
  ...props
}: Readonly<{ children: ReactNode; className?: string }> &
  React.ComponentProps<"button">) => {
  const id = useId();

  return (
    <button
      id={id}
      className="group relative w-full cursor-pointer overflow-hidden rounded-full bg-neutral-200 p-[2px] transition-transform active:scale-95 disabled:cursor-not-allowed disabled:opacity-50 dark:bg-neutral-600"
      type="button"
      {...props}
    >
      <motion.span
        animate={{
          top: ["50%", "0%", "50%", "100%", "50%"],
          left: ["0%", "50%", "100%", "50%", "0%"],
        }}
        className="absolute z-10 size-8 -translate-x-1/2 -translate-y-1/2 transform-gpu blur-xs transition-transform duration-300 group-hover:scale-3"
        initial={{ top: 0, left: 0 }}
        transition={{
          duration: 3,
          ease: "linear",
          repeat: Number.POSITIVE_INFINITY,
        }}
      >
        <motion.span
          animate={{
            rotate: ["0deg", "360deg"],
          }}
          className="block size-full transform-gpu rounded-full"
          style={{
            background:
              "linear-gradient(135deg, #3BC4F2, #7A69F9, #F26378, #F5833F)",
          }}
          transition={{
            duration: 3,
            ease: "linear",
            repeat: Number.POSITIVE_INFINITY,
          }}
        />
      </motion.span>
      <span className="relative z-10 block rounded-full bg-neutral-100 px-3 py-1 dark:bg-neutral-800">
        <motion.span
          animate={{
            backgroundImage: [
              "linear-gradient(90deg, #3BC4F2, #7A69F9, #F26378, #F5833F)",
              "linear-gradient(90deg, #F5833F,#3BC4F2, #7A69F9, #F26378)",
              "linear-gradient(90deg, #F26378, #F5833F,#3BC4F2, #7A69F9)",
              "linear-gradient(90deg, #7A69F9, #F26378, #F5833F,#3BC4F2)",
              "linear-gradient(90deg, #3BC4F2, #7A69F9, #F26378, #F5833F)",
            ],
          }}
          className={cn(
            "flex transform-gpu items-center justify-center bg-clip-text p-2 text-sm tracking-tighter transition-colors duration-500 group-hover:text-transparent",
            className,
          )}
          transition={{
            duration: 1,
            ease: "linear",
            repeat: Number.POSITIVE_INFINITY,
          }}
        >
          {children}
        </motion.span>
      </span>
    </button>
  );
};
