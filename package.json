{"name": "next-core", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "make": "next build", "go": "cross-env NODE_ENV=production node server.js", "err": "bunx tsc --noEmit && next lint", "format": "prettier --write .", "gen": "bunx prisma generate", "push": "bunx prisma db push", "mig": "bunx prisma db migrate", "stu": "bunx prisma studio", "reset": "bunx prisma migrate reset"}, "dependencies": {"@bprogress/next": "^3.2.12", "@hookform/resolvers": "^5.2.0", "@polar-sh/better-auth": "^1.0.8", "@polar-sh/sdk": "^0.34.8", "@prisma/client": "^6.12.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-compose-refs": "^1.1.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-primitive": "^2.1.3", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@remixicon/react": "^4.6.0", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/nodemailer": "^6.4.17", "better-auth": "^1.3.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cobe": "^0.6.4", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "lenis": "^1.3.8", "lucide-react": "^0.525.0", "motion": "^12.23.11", "next": "15.3.4", "next-themes": "^0.4.6", "nodemailer": "^7.0.5", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "prisma": "^6.12.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.61.1", "recharts": "2.15.4", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "virtua": "^0.41.5", "zod": "^3.25.76"}, "devDependencies": {"@better-auth/cli": "^1.3.4", "@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^20.19.9", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "cross-env": "^7.0.3", "eslint": "^9.32.0", "eslint-config-next": "15.3.4", "tailwindcss": "^4.1.11", "tsx": "^4.20.3", "tw-animate-css": "^1.3.6", "typescript": "^5.8.3"}}