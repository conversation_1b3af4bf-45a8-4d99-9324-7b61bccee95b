import { RiRobot3Fill } from "@remixicon/react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

export default function AiChatbotSupport() {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          size="icon"
          className="fixed right-6 bottom-6 z-40 size-12 rounded-full"
        >
          <RiRobot3Fill className="h-5 w-5" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        side="top"
        sideOffset={10}
        align="end"
        className="bg-background/95 border-border/50 w-[380px] truncate rounded-2xl border p-2 shadow-xl backdrop-blur-xl md:w-[400px] lg:w-[420px]"
      >
        <div className="bg-muted/30 flex flex-col space-y-2 rounded-2xl border border-dashed border-neutral-400/20">
          <div className="h-[280px] overflow-y-auto p-3">
            <div className="space-y-3">
              <div className="flex items-start gap-2">
                <RiRobot3Fill className="text-primary/70 mt-0.5 h-4 w-4 shrink-0" />
                <p className="text-foreground/90 text-xs leading-relaxed">
                  Hello! How can I help you today?
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-1.5 p-2">
            <input
              type="text"
              autoFocus
              placeholder="Ask anything..."
              className="border-border/40 h-8 flex-1 bg-none px-2 text-sm outline-none"
            />
            <button
              type="button"
              className="bg-primary/10 hover:bg-primary/20 border-border/40 flex h-8 w-8 items-center justify-center rounded-2xl border"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 16 16"
                fill="none"
                className="text-primary h-3.5 w-3.5"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M1.5 8h13m0 0L8 1.5M14.5 8L8 14.5"
                />
              </svg>
            </button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
