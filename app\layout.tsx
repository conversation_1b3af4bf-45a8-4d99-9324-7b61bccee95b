import type { Metadata } from "next";

import "@/app/globals.css";
import Providers from "@/components/providers";

export const metadata: Metadata = {
  title: "Next Core",
  description:
    "Next Core is a productivity app that helps you be more productive and successful.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html
      lang="en"
      suppressHydrationWarning
    >
      <head>
        <link
          rel="icon"
          href="/icon"
          type="image/svg+xml"
          sizes="36x36"
        />
      </head>
      <body className="relative scroll-smooth font-sans antialiased">
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
