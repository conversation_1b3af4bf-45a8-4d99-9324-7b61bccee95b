"use server";

import GetAuthSession from "@/action/auth/get-auth-session";
import { polarClient } from "@/lib/polar";

export default async function GetCurrentSubscription() {
  const session = await GetAuthSession();

  if (!session) {
    return;
  }

  const customer = await polarClient.customers.getStateExternal({
    externalId: session.user.id,
  });

  const subscriptions = customer.activeSubscriptions[0];

  if (!subscriptions) {
    return;
  }

  const product = await polarClient.products.get({
    id: subscriptions.productId,
  });

  return product;
}
