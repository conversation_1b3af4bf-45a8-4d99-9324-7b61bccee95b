import { ChartAreaInteractive } from "@/components/dashboard/chart-area-interactive";
import { ChartBarInteractive } from "@/components/dashboard/chart-bar-interactive";
import { ChartRadarMultiple } from "@/components/dashboard/chart-radar-multiple";
import { ChartRadialLabel } from "@/components/dashboard/chart-radial-label";

export default async function DashboardPage() {
  return (
    <div className="grid gap-2 md:grid-cols-1 lg:grid-cols-2">
      {/* Chart 1 */}
      <ChartRadialLabel />
      {/* Chart 2 */}
      <ChartRadarMultiple />
      {/* Chart 4 - Wider chart */}
      <ChartAreaInteractive />
      {/* Chart 4 - Wider chart */}
      <ChartBarInteractive />
    </div>
  );
}
