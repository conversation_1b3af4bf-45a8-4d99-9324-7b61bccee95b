import GetAuthSession from "@/action/auth/get-auth-session";
import LenisProvider from "@/components/lenis-provider";
import Footer from "@/components/root/footer";
import Header from "@/components/root/header";

export default async function BaseLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const session = await GetAuthSession();

  return (
    <>
      <LenisProvider
        root={true}
        options={{ smoothWheel: true }}
      >
        <Header session={session} />
        {children}
        <Footer />
      </LenisProvider>
    </>
  );
}
