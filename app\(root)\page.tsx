import GetAuthSession from "@/action/auth/get-auth-session";
import CTASection from "@/components/root/cta-section";
import FeaturesSection from "@/components/root/features-section";
import HeroSection from "@/components/root/hero-section";
import PricingSection from "@/components/root/pricing-section";
import TestimonialsSection from "@/components/root/testimonials-section";

export default async function RootPage() {
  const session = await GetAuthSession();

  return (
    <main className="min-h-screen space-y-0 md:space-y-6 lg:space-y-12">
      <HeroSection />

      <FeaturesSection />

      <PricingSection session={session} />

      <TestimonialsSection />

      <CTASection />
    </main>
  );
}
